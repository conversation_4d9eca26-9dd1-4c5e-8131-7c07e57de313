import type { APIRoute } from 'astro';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get URL parameters
    const url = new URL(request.url);
    const dateRange = url.searchParams.get('range') || '7d';

    // Calculate date filter based on range
    let dateFilter = '';
    switch (dateRange) {
      case '1d':
        dateFilter = "AND scan_time >= datetime('now', '-1 day')";
        break;
      case '7d':
        dateFilter = "AND scan_time >= datetime('now', '-7 days')";
        break;
      case '30d':
        dateFilter = "AND scan_time >= datetime('now', '-30 days')";
        break;
      case '90d':
        dateFilter = "AND scan_time >= datetime('now', '-90 days')";
        break;
      case '1y':
        dateFilter = "AND scan_time >= datetime('now', '-1 year')";
        break;
      default:
        dateFilter = '';
    }

    // Get total scans for percentage calculations
    const totalScansResult = await db.prepare(`
      SELECT COUNT(*) as count 
      FROM qr_code_scan_analytics 
      WHERE 1=1 ${dateFilter}
    `).first();
    const totalScans = totalScansResult?.count || 1; // Avoid division by zero

    // Get device types
    const deviceTypesResult = await db.prepare(`
      SELECT 
        COALESCE(device, 'Unknown') as device,
        COUNT(*) as scans,
        COUNT(DISTINCT ip) as uniqueUsers,
        ROUND(COUNT(*) * 100.0 / ?, 2) as percentage
      FROM qr_code_scan_analytics 
      WHERE 1=1 ${dateFilter}
      GROUP BY device
      ORDER BY scans DESC
    `).bind(totalScans).all();

    // Get operating systems
    const operatingSystemsResult = await db.prepare(`
      SELECT 
        COALESCE(os, 'Unknown') as os,
        COUNT(*) as scans,
        COUNT(DISTINCT ip) as uniqueUsers,
        ROUND(COUNT(*) * 100.0 / ?, 2) as percentage
      FROM qr_code_scan_analytics 
      WHERE 1=1 ${dateFilter}
      GROUP BY os
      ORDER BY scans DESC
    `).bind(totalScans).all();

    // Get browsers
    const browsersResult = await db.prepare(`
      SELECT 
        COALESCE(browser, 'Unknown') as browser,
        COUNT(*) as scans,
        COUNT(DISTINCT ip) as uniqueUsers,
        ROUND(COUNT(*) * 100.0 / ?, 2) as percentage
      FROM qr_code_scan_analytics 
      WHERE 1=1 ${dateFilter}
      GROUP BY browser
      ORDER BY scans DESC
    `).bind(totalScans).all();

    // Calculate mobile vs desktop percentages
    const mobileScansResult = await db.prepare(`
      SELECT COUNT(*) as count 
      FROM qr_code_scan_analytics 
      WHERE (LOWER(device) LIKE '%mobile%' OR LOWER(device) LIKE '%phone%') ${dateFilter}
    `).first();
    const mobileScans = mobileScansResult?.count || 0;

    const desktopScansResult = await db.prepare(`
      SELECT COUNT(*) as count 
      FROM qr_code_scan_analytics 
      WHERE (LOWER(device) LIKE '%desktop%' OR LOWER(device) LIKE '%computer%') ${dateFilter}
    `).first();
    const desktopScans = desktopScansResult?.count || 0;

    const mobilePercentage = totalScans > 0 ? (mobileScans / totalScans) * 100 : 0;
    const desktopPercentage = totalScans > 0 ? (desktopScans / totalScans) * 100 : 0;

    const deviceData = {
      deviceTypes: deviceTypesResult.results || [],
      operatingSystems: operatingSystemsResult.results || [],
      browsers: browsersResult.results || [],
      totalScans,
      mobilePercentage: Math.round(mobilePercentage * 100) / 100,
      desktopPercentage: Math.round(desktopPercentage * 100) / 100
    };

    return new Response(JSON.stringify(deviceData), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Device analytics API error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to fetch device analytics' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
