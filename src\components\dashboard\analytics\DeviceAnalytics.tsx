import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Badge } from '../../ui/badge';
import { Button } from '../../ui/button';
import { Progress } from '../../ui/progress';
import { 
  Smartphone, 
  Monitor, 
  Tablet, 
  Chrome,
  RefreshCw,
  TrendingUp,
  Users
} from 'lucide-react';
import {
  Pie<PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend
} from 'recharts';

interface DeviceData {
  deviceTypes: Array<{
    device: string;
    scans: number;
    percentage: number;
    uniqueUsers: number;
  }>;
  operatingSystems: Array<{
    os: string;
    scans: number;
    percentage: number;
    uniqueUsers: number;
  }>;
  browsers: Array<{
    browser: string;
    scans: number;
    percentage: number;
    uniqueUsers: number;
  }>;
  totalScans: number;
  mobilePercentage: number;
  desktopPercentage: number;
}

interface DeviceAnalyticsProps {
  dateRange: string;
}

const DEVICE_COLORS = {
  mobile: '#10B981',
  desktop: '#3B82F6',
  tablet: '#F59E0B',
  unknown: '#6B7280'
};

const DEVICE_ICONS = {
  mobile: <Smartphone className="h-5 w-5" />,
  desktop: <Monitor className="h-5 w-5" />,
  tablet: <Tablet className="h-5 w-5" />,
  unknown: <Chrome className="h-5 w-5" />
};

export const DeviceAnalytics: React.FC<DeviceAnalyticsProps> = ({ dateRange }) => {
  const [data, setData] = useState<DeviceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDeviceData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/analytics/devices?range=${dateRange}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch device data');
      }

      const result = await response.json();
      setData(result);
      setError(null);
    } catch (err) {
      console.error('Error fetching device data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch device data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDeviceData();
  }, [dateRange]);

  const getDeviceIcon = (deviceType: string) => {
    const type = deviceType.toLowerCase();
    return DEVICE_ICONS[type as keyof typeof DEVICE_ICONS] || DEVICE_ICONS.unknown;
  };

  const getDeviceColor = (deviceType: string) => {
    const type = deviceType.toLowerCase();
    return DEVICE_COLORS[type as keyof typeof DEVICE_COLORS] || DEVICE_COLORS.unknown;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-64 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Smartphone className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Device Data</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={fetchDeviceData} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) return null;

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Smartphone className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Mobile Usage</p>
                <p className="text-2xl font-bold text-gray-900">{data.mobilePercentage.toFixed(1)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Monitor className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Desktop Usage</p>
                <p className="text-2xl font-bold text-gray-900">{data.desktopPercentage.toFixed(1)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        {/* Device Types Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Smartphone className="h-5 w-5" />
              <span>Device Types</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={data.deviceTypes}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  innerRadius={40}
                  paddingAngle={2}
                  dataKey="scans"
                  nameKey="device"
                >
                  {data.deviceTypes.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={getDeviceColor(entry.device)}
                    />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: any, name: any) => [
                    `${value.toLocaleString()} scans`,
                    name
                  ]}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Operating Systems */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Monitor className="h-5 w-5" />
              <span>Operating Systems</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={data.operatingSystems.slice(0, 8)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="os" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip 
                  formatter={(value: any) => [`${value.toLocaleString()} scans`, 'Scans']}
                />
                <Bar 
                  dataKey="scans" 
                  fill="#8B5CF6"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Device Breakdown Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Device Breakdown</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.deviceTypes.map((device, index) => (
              <div key={device.device} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg" style={{ backgroundColor: `${getDeviceColor(device.device)}20` }}>
                    {getDeviceIcon(device.device)}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 capitalize">{device.device}</p>
                    <p className="text-sm text-gray-600">{device.uniqueUsers.toLocaleString()} unique users</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">{device.scans.toLocaleString()}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <Progress 
                      value={device.percentage} 
                      className="w-20 h-2"
                    />
                    <span className="text-sm text-gray-600">{device.percentage.toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Browser Analytics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Chrome className="h-5 w-5" />
            <span>Browser Distribution</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            {data.browsers.slice(0, 9).map((browser, index) => (
              <div key={browser.browser} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{browser.browser}</h4>
                  <Badge variant="secondary">
                    {browser.percentage.toFixed(1)}%
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Scans</span>
                    <span className="font-medium">{browser.scans.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Users</span>
                    <span className="font-medium">{browser.uniqueUsers.toLocaleString()}</span>
                  </div>
                  <Progress value={browser.percentage} className="h-2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* OS Detailed Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Operating System Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Operating System</th>
                  <th className="text-right py-3 px-4 font-semibold text-gray-700">Scans</th>
                  <th className="text-right py-3 px-4 font-semibold text-gray-700">Unique Users</th>
                  <th className="text-right py-3 px-4 font-semibold text-gray-700">Percentage</th>
                </tr>
              </thead>
              <tbody>
                {data.operatingSystems.map((os, index) => (
                  <tr key={os.os} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-purple-500 rounded-full" />
                        <span className="font-medium">{os.os}</span>
                      </div>
                    </td>
                    <td className="text-right py-3 px-4 font-mono">
                      {os.scans.toLocaleString()}
                    </td>
                    <td className="text-right py-3 px-4 font-mono">
                      {os.uniqueUsers.toLocaleString()}
                    </td>
                    <td className="text-right py-3 px-4">
                      <Badge variant="outline">
                        {os.percentage.toFixed(1)}%
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DeviceAnalytics;
