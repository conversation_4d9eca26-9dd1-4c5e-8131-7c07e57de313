{"name": "qranalytica-new-astro-project", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "dev:cf": "astro build && wrangler pages dev --compatibility-date=2025-06-17", "build": "astro build", "preview": "astro build && wrangler pages dev", "astro": "astro", "deploy": "astro build && wrangler pages deploy", "cf-typegen": "wrangler types"}, "dependencies": {"@astrojs/cloudflare": "^12.6.0", "@astrojs/react": "^4.3.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "arctic": "^3.7.0", "astro": "^5.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "jose": "^5.3.0", "lucide-react": "^0.522.0", "next-themes": "^0.4.6", "qr-code-styling": "^1.6.1", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.4", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "uuid": "^9.0.1", "vaul": "^1.1.2", "zod": "^3.25.67", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^24.0.3", "@types/uuid": "^9.0.7", "tw-animate-css": "^1.3.4", "wrangler": "^4.24.3"}}