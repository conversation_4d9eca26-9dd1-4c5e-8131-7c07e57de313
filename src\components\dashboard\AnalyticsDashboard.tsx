import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '../ui/tabs';
import { 
  BarChart3, 
  Globe, 
  Smartphone, 
  Clock, 
  TrendingUp, 
  Users, 
  Activity,
  Download,
  RefreshCw,
  Calendar,
  Eye,
  MousePointer
} from 'lucide-react';
import { RealTimeMetrics } from './analytics/RealTimeMetrics';
import { GeographicAnalytics } from './analytics/GeographicAnalytics';
import { TimeBasedAnalytics } from './analytics/TimeBasedAnalytics';
import { DeviceAnalytics } from './analytics/DeviceAnalytics';
import { ReportingTools } from './analytics/ReportingTools';
import { ActivityFeed } from './analytics/ActivityFeed';

interface AnalyticsDashboardProps {}

interface AnalyticsOverview {
  totalScans: number;
  uniqueUsers: number;
  totalQRCodes: number;
  scanVelocity: {
    hourly: number;
    daily: number;
  };
  conversionRate: number;
  topPerformers: Array<{
    id: string;
    name: string;
    scans: number;
    change: number;
  }>;
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = () => {
  const [overview, setOverview] = useState<AnalyticsOverview | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDateRange, setSelectedDateRange] = useState('7d');

  // Fetch analytics overview data
  const fetchOverview = async () => {
    try {
      setRefreshing(true);
      const response = await fetch('/api/analytics/overview', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics overview');
      }

      const data = await response.json();
      setOverview(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching analytics overview:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchOverview();
    
    // Set up auto-refresh every 30 seconds for real-time data
    const interval = setInterval(fetchOverview, 30000);
    return () => clearInterval(interval);
  }, [selectedDateRange]);

  const handleRefresh = () => {
    fetchOverview();
  };

  const handleExportData = async (format: 'csv' | 'excel') => {
    try {
      const response = await fetch(`/api/analytics/export?format=${format}&range=${selectedDateRange}`, {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Export failed');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `qr-analytics-${selectedDateRange}.${format === 'excel' ? 'xlsx' : 'csv'}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Export error:', err);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
            <p className="text-gray-600 mt-1">Comprehensive QR code performance insights</p>
          </div>
        </div>
        
        <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <BarChart3 className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Analytics</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={handleRefresh} className="bg-blue-600 hover:bg-blue-700">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">Comprehensive QR code performance insights</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center space-x-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </Button>
          
          <Button
            onClick={() => handleExportData('excel')}
            className="bg-green-600 hover:bg-green-700 text-white flex items-center space-x-2"
          >
            <Download className="h-4 w-4" />
            <span>Export</span>
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      {overview && <RealTimeMetrics overview={overview} />}

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="geographic" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="geographic" className="flex items-center space-x-2">
            <Globe className="h-4 w-4" />
            <span>Geographic</span>
          </TabsTrigger>
          <TabsTrigger value="time-based" className="flex items-center space-x-2">
            <Clock className="h-4 w-4" />
            <span>Time Analysis</span>
          </TabsTrigger>
          <TabsTrigger value="devices" className="flex items-center space-x-2">
            <Smartphone className="h-4 w-4" />
            <span>Devices</span>
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>Activity</span>
          </TabsTrigger>
          <TabsTrigger value="reports" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Reports</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="geographic" className="space-y-6">
          <GeographicAnalytics dateRange={selectedDateRange} />
        </TabsContent>

        <TabsContent value="time-based" className="space-y-6">
          <TimeBasedAnalytics dateRange={selectedDateRange} />
        </TabsContent>

        <TabsContent value="devices" className="space-y-6">
          <DeviceAnalytics dateRange={selectedDateRange} />
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <ActivityFeed dateRange={selectedDateRange} />
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <ReportingTools 
            dateRange={selectedDateRange}
            onDateRangeChange={setSelectedDateRange}
            onExport={handleExportData}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalyticsDashboard;
