import type { APIRoute } from 'astro';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get URL parameters
    const url = new URL(request.url);
    const format = url.searchParams.get('format') || 'csv';
    const dateRange = url.searchParams.get('range') || '7d';

    // Calculate date filter based on range
    let dateFilter = '';
    switch (dateRange) {
      case '1d':
        dateFilter = "AND scan.scan_time >= datetime('now', '-1 day')";
        break;
      case '7d':
        dateFilter = "AND scan.scan_time >= datetime('now', '-7 days')";
        break;
      case '30d':
        dateFilter = "AND scan.scan_time >= datetime('now', '-30 days')";
        break;
      case '90d':
        dateFilter = "AND scan.scan_time >= datetime('now', '-90 days')";
        break;
      case '1y':
        dateFilter = "AND scan.scan_time >= datetime('now', '-1 year')";
        break;
      default:
        dateFilter = '';
    }

    // Get comprehensive analytics data
    const analyticsResult = await db.prepare(`
      SELECT 
        scan.id,
        scan.qr_code_id,
        qr.name as qr_name,
        qr.content_type,
        scan.scan_time,
        scan.ip,
        scan.country,
        scan.city,
        scan.device,
        scan.os,
        scan.browser,
        scan.user_agent,
        scan.referrer
      FROM qr_code_scan_analytics scan
      LEFT JOIN qr_codes qr ON scan.qr_code_id = qr.id
      WHERE 1=1 ${dateFilter}
      ORDER BY scan.scan_time DESC
    `).all();

    const data = analyticsResult.results || [];

    if (format === 'csv') {
      // Generate CSV
      const headers = [
        'ID',
        'QR Code ID',
        'QR Code Name',
        'Content Type',
        'Scan Time',
        'IP Address',
        'Country',
        'City',
        'Device',
        'Operating System',
        'Browser',
        'User Agent',
        'Referrer'
      ];

      const csvRows = [
        headers.join(','),
        ...data.map((row: any) => [
          row.id,
          row.qr_code_id,
          `"${row.qr_name || ''}"`,
          row.content_type || '',
          row.scan_time,
          row.ip || '',
          row.country || '',
          row.city || '',
          row.device || '',
          row.os || '',
          row.browser || '',
          `"${row.user_agent || ''}"`,
          `"${row.referrer || ''}"`
        ].join(','))
      ];

      const csvContent = csvRows.join('\n');

      return new Response(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="qr-analytics-${dateRange}.csv"`
        }
      });
    } else if (format === 'excel') {
      // For Excel format, we'll return CSV with Excel MIME type
      // In a real implementation, you might want to use a library like xlsx
      const headers = [
        'ID',
        'QR Code ID',
        'QR Code Name',
        'Content Type',
        'Scan Time',
        'IP Address',
        'Country',
        'City',
        'Device',
        'Operating System',
        'Browser',
        'User Agent',
        'Referrer'
      ];

      const csvRows = [
        headers.join('\t'), // Use tabs for Excel
        ...data.map((row: any) => [
          row.id,
          row.qr_code_id,
          row.qr_name || '',
          row.content_type || '',
          row.scan_time,
          row.ip || '',
          row.country || '',
          row.city || '',
          row.device || '',
          row.os || '',
          row.browser || '',
          row.user_agent || '',
          row.referrer || ''
        ].join('\t'))
      ];

      const excelContent = csvRows.join('\n');

      return new Response(excelContent, {
        status: 200,
        headers: {
          'Content-Type': 'application/vnd.ms-excel',
          'Content-Disposition': `attachment; filename="qr-analytics-${dateRange}.xls"`
        }
      });
    } else {
      return new Response(JSON.stringify({ error: 'Unsupported format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Export API error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to export data' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
