import type { APIRoute } from 'astro';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get URL parameters
    const url = new URL(request.url);
    const dateRange = url.searchParams.get('range') || '7d';
    const qrCodeId = url.searchParams.get('qr_code_id');

    // Calculate date filter based on range
    let dateFilter = '';
    let daysBack = 7;
    switch (dateRange) {
      case '1d':
        dateFilter = "AND scan_time >= datetime('now', '-1 day')";
        daysBack = 1;
        break;
      case '7d':
        dateFilter = "AND scan_time >= datetime('now', '-7 days')";
        daysBack = 7;
        break;
      case '30d':
        dateFilter = "AND scan_time >= datetime('now', '-30 days')";
        daysBack = 30;
        break;
      case '90d':
        dateFilter = "AND scan_time >= datetime('now', '-90 days')";
        daysBack = 90;
        break;
      case '1y':
        dateFilter = "AND scan_time >= datetime('now', '-1 year')";
        daysBack = 365;
        break;
      default:
        dateFilter = '';
        daysBack = 30;
    }

    // Add QR code filter if specified
    const qrCodeFilter = qrCodeId ? "AND qr_code_id = ?" : "";

    // Get hourly data (24 hours)
    let hourlyQuery = `
      SELECT
        CAST(strftime('%H', scan_time) AS INTEGER) as hour,
        COUNT(*) as scans
      FROM qr_code_scan_analytics
      WHERE 1=1 ${dateFilter} ${qrCodeFilter}
      GROUP BY hour
      ORDER BY hour
    `;
    const hourlyResult = qrCodeId ?
      await db.prepare(hourlyQuery).bind(qrCodeId).all() :
      await db.prepare(hourlyQuery).all();

    // Create complete hourly data (0-23 hours)
    const hourlyData = Array.from({ length: 24 }, (_, hour) => {
      const data = (hourlyResult.results || []).find((h: any) => h.hour === hour);
      const period = hour >= 12 ? 'PM' : 'AM';
      const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
      return {
        hour,
        scans: data?.scans || 0,
        label: `${displayHour}${period}`
      };
    });

    // Get daily data
    let dailyQuery = `
      SELECT
        DATE(scan_time) as date,
        COUNT(*) as scans,
        COUNT(DISTINCT ip) as uniqueUsers
      FROM qr_code_scan_analytics
      WHERE 1=1 ${dateFilter} ${qrCodeFilter}
      GROUP BY DATE(scan_time)
      ORDER BY date
    `;
    const dailyResult = qrCodeId ?
      await db.prepare(dailyQuery).bind(qrCodeId).all() :
      await db.prepare(dailyQuery).all();

    // Get weekly data
    let weeklyQuery = `
      SELECT
        strftime('%Y-W%W', scan_time) as week,
        COUNT(*) as scans,
        COUNT(DISTINCT ip) as uniqueUsers
      FROM qr_code_scan_analytics
      WHERE 1=1 ${dateFilter} ${qrCodeFilter}
      GROUP BY week
      ORDER BY week
    `;
    const weeklyResult = qrCodeId ?
      await db.prepare(weeklyQuery).bind(qrCodeId).all() :
      await db.prepare(weeklyQuery).all();

    // Get monthly data
    let monthlyQuery = `
      SELECT
        strftime('%Y-%m', scan_time) as month,
        COUNT(*) as scans,
        COUNT(DISTINCT ip) as uniqueUsers
      FROM qr_code_scan_analytics
      WHERE 1=1 ${dateFilter} ${qrCodeFilter}
      GROUP BY month
      ORDER BY month
    `;
    const monthlyResult = qrCodeId ?
      await db.prepare(monthlyQuery).bind(qrCodeId).all() :
      await db.prepare(monthlyQuery).all();

    // Calculate peak hours
    const peakHours = [...hourlyData]
      .sort((a, b) => b.scans - a.scans)
      .slice(0, 6);

    // Calculate statistics
    let totalScansQuery = `
      SELECT COUNT(*) as count
      FROM qr_code_scan_analytics
      WHERE 1=1 ${dateFilter} ${qrCodeFilter}
    `;
    const totalScansResult = qrCodeId ?
      await db.prepare(totalScansQuery).bind(qrCodeId).first() :
      await db.prepare(totalScansQuery).first();
    const totalScans = totalScansResult?.count || 0;

    const averageDaily = daysBack > 0 ? totalScans / daysBack : 0;

    // Find peak day
    const peakDayData = (dailyResult.results || []).reduce((max: any, current: any) => {
      return (current.scans > (max?.scans || 0)) ? current : max;
    }, null);

    const peakDay = peakDayData ? {
      date: peakDayData.date,
      scans: peakDayData.scans
    } : {
      date: new Date().toISOString().split('T')[0],
      scans: 0
    };

    const timeBasedData = {
      hourlyData,
      dailyData: dailyResult.results || [],
      weeklyData: weeklyResult.results || [],
      monthlyData: monthlyResult.results || [],
      peakHours,
      totalScans,
      averageDaily: Math.round(averageDaily),
      peakDay
    };

    return new Response(JSON.stringify(timeBasedData), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Time-based analytics API error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to fetch time-based analytics' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
