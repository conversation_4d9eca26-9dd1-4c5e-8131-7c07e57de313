import type { APIRoute } from 'astro';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get URL parameters
    const url = new URL(request.url);
    const dateRange = url.searchParams.get('range') || '7d';
    const qrCodeId = url.searchParams.get('qr_code_id');

    // Calculate date filter based on range
    let dateFilter = '';
    switch (dateRange) {
      case '1d':
        dateFilter = "AND scan_time >= datetime('now', '-1 day')";
        break;
      case '7d':
        dateFilter = "AND scan_time >= datetime('now', '-7 days')";
        break;
      case '30d':
        dateFilter = "AND scan_time >= datetime('now', '-30 days')";
        break;
      case '90d':
        dateFilter = "AND scan_time >= datetime('now', '-90 days')";
        break;
      case '1y':
        dateFilter = "AND scan_time >= datetime('now', '-1 year')";
        break;
      default:
        dateFilter = '';
    }

    // Add QR code filter if specified
    const qrCodeFilter = qrCodeId ? "AND qr_code_id = ?" : "";

    // Get total scans for percentage calculations
    let totalScansQuery = `
      SELECT COUNT(*) as count
      FROM qr_code_scan_analytics
      WHERE country IS NOT NULL ${dateFilter} ${qrCodeFilter}
    `;
    const totalScansResult = qrCodeId ?
      await db.prepare(totalScansQuery).bind(qrCodeId).first() :
      await db.prepare(totalScansQuery).first();
    const totalScans = totalScansResult?.count || 1; // Avoid division by zero

    // Get countries with scan counts
    let countriesQuery = `
      SELECT
        country,
        COUNT(*) as scans,
        ROUND(COUNT(*) * 100.0 / ?, 2) as percentage
      FROM qr_code_scan_analytics
      WHERE country IS NOT NULL ${dateFilter} ${qrCodeFilter}
      GROUP BY country
      ORDER BY scans DESC
    `;
    const countriesResult = qrCodeId ?
      await db.prepare(countriesQuery).bind(totalScans, qrCodeId).all() :
      await db.prepare(countriesQuery).bind(totalScans).all();

    // Get cities for each country
    const countries = [];
    for (const country of (countriesResult.results || [])) {
      let citiesQuery = `
        SELECT
          city,
          COUNT(*) as scans,
          ROUND(COUNT(*) * 100.0 / ?, 2) as percentage
        FROM qr_code_scan_analytics
        WHERE country = ? AND city IS NOT NULL ${dateFilter} ${qrCodeFilter}
        GROUP BY city
        ORDER BY scans DESC
        LIMIT 10
      `;
      const citiesResult = qrCodeId ?
        await db.prepare(citiesQuery).bind(country.scans, country.country, qrCodeId).all() :
        await db.prepare(citiesQuery).bind(country.scans, country.country).all();

      countries.push({
        country: country.country,
        scans: country.scans,
        percentage: country.percentage,
        cities: citiesResult.results || []
      });
    }

    // Get top cities globally
    let topCitiesQuery = `
      SELECT
        city,
        country,
        COUNT(*) as scans,
        ROUND(COUNT(*) * 100.0 / ?, 2) as percentage
      FROM qr_code_scan_analytics
      WHERE city IS NOT NULL AND country IS NOT NULL ${dateFilter} ${qrCodeFilter}
      GROUP BY city, country
      ORDER BY scans DESC
      LIMIT 20
    `;
    const topCitiesResult = qrCodeId ?
      await db.prepare(topCitiesQuery).bind(totalScans, qrCodeId).all() :
      await db.prepare(topCitiesQuery).bind(totalScans).all();

    const geographicData = {
      countries,
      topCities: topCitiesResult.results || [],
      totalScans
    };

    return new Response(JSON.stringify(geographicData), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Geographic analytics API error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to fetch geographic analytics' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
