import type { APIRoute } from 'astro';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get URL parameters
    const url = new URL(request.url);
    const dateRange = url.searchParams.get('range') || '7d';

    // Calculate date filter based on range
    let dateFilter = '';
    switch (dateRange) {
      case '1d':
        dateFilter = "AND scan_time >= datetime('now', '-1 day')";
        break;
      case '7d':
        dateFilter = "AND scan_time >= datetime('now', '-7 days')";
        break;
      case '30d':
        dateFilter = "AND scan_time >= datetime('now', '-30 days')";
        break;
      case '90d':
        dateFilter = "AND scan_time >= datetime('now', '-90 days')";
        break;
      case '1y':
        dateFilter = "AND scan_time >= datetime('now', '-1 year')";
        break;
      default:
        dateFilter = '';
    }

    // Get total scans for percentage calculations
    const totalScansResult = await db.prepare(`
      SELECT COUNT(*) as count 
      FROM qr_code_scan_analytics 
      WHERE country IS NOT NULL ${dateFilter}
    `).first();
    const totalScans = totalScansResult?.count || 1; // Avoid division by zero

    // Get countries with scan counts
    const countriesResult = await db.prepare(`
      SELECT 
        country,
        COUNT(*) as scans,
        ROUND(COUNT(*) * 100.0 / ?, 2) as percentage
      FROM qr_code_scan_analytics 
      WHERE country IS NOT NULL ${dateFilter}
      GROUP BY country
      ORDER BY scans DESC
    `).bind(totalScans).all();

    // Get cities for each country
    const countries = [];
    for (const country of (countriesResult.results || [])) {
      const citiesResult = await db.prepare(`
        SELECT 
          city,
          COUNT(*) as scans,
          ROUND(COUNT(*) * 100.0 / ?, 2) as percentage
        FROM qr_code_scan_analytics 
        WHERE country = ? AND city IS NOT NULL ${dateFilter}
        GROUP BY city
        ORDER BY scans DESC
        LIMIT 10
      `).bind(country.scans, country.country).all();

      countries.push({
        country: country.country,
        scans: country.scans,
        percentage: country.percentage,
        cities: citiesResult.results || []
      });
    }

    // Get top cities globally
    const topCitiesResult = await db.prepare(`
      SELECT 
        city,
        country,
        COUNT(*) as scans,
        ROUND(COUNT(*) * 100.0 / ?, 2) as percentage
      FROM qr_code_scan_analytics 
      WHERE city IS NOT NULL AND country IS NOT NULL ${dateFilter}
      GROUP BY city, country
      ORDER BY scans DESC
      LIMIT 20
    `).bind(totalScans).all();

    const geographicData = {
      countries,
      topCities: topCitiesResult.results || [],
      totalScans
    };

    return new Response(JSON.stringify(geographicData), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Geographic analytics API error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to fetch geographic analytics' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
